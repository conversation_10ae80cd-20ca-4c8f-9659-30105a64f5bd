# CSP 配置修复建议

## 当前问题
你的 CSP 配置中存在语法错误，导致浏览器无法正确解析 CSP 指令。

## 当前配置（有问题）
```nginx
add_header Content-Security-Policy "default-src 'self'; connect-src 'self' ws: wss:; font-src * data: blob:;img-src * data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-hashes' 'unsafe-inline';";
```

## 修复后的配置

### 方案1：最小修复（保持你的原始意图）
```nginx
add_header Content-Security-Policy "default-src 'self'; connect-src 'self' ws: wss:; font-src * data: blob:; img-src * data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-hashes' 'unsafe-inline';";
```

### 方案2：更安全的配置（推荐）
```nginx
add_header Content-Security-Policy "default-src 'self'; connect-src 'self' ws: wss: http: https:; font-src 'self' data: blob: http: https: *.alicdn.com; img-src 'self' data: blob: http: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' http: https:; style-src 'self' 'unsafe-inline' 'unsafe-hashes' http: https:;";
```

## 主要修复点

1. **添加缺失的空格**：
   - `font-src * data: blob:;img-src` → `font-src * data: blob:; img-src`
   - `script-src * 'unsafe-inline''unsafe-eval'` → `script-src * 'unsafe-inline' 'unsafe-eval'`
   - `style-src * 'unsafe-hashes''unsafe-inline'` → `style-src * 'unsafe-hashes' 'unsafe-inline'`

2. **针对阿里云字体的特殊处理**：
   - 添加 `*.alicdn.com` 到 `font-src` 以支持阿里云图标字体

## 重启 nginx
修改配置后，记得重启 nginx：
```bash
sudo nginx -t  # 测试配置
sudo nginx -s reload  # 重新加载配置
```
